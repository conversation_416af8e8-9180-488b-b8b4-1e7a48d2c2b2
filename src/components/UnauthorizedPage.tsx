import { Link, useNavigate } from "react-router-dom";

export default function UnauthorizedPage() {
    const navigate = useNavigate();

    const handleGoBack = () => {
        navigate(-1);
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
            <div className="max-w-md w-full text-center">
                {/* 401 Image */}
                <div className="mb-8">
                    <img
                        src="/401.png"
                        alt="401 Unauthorized"
                        className="mx-auto h-32 w-auto"
                    />
                </div>

                {/* Error Message */}
                <h1 className="text-6xl font-bold text-gray-900 mb-4">401</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">Unauthorized Access</h2>
                <p className="text-gray-600 mb-8">
                    You don't have permission to access this resource. Please log in with valid credentials or contact an administrator.
                </p>

                {/* Action Buttons */}
                <div className="space-y-4">
                    <Link 
                        to="/" 
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 inline-block"
                    >
                        Go to Home
                    </Link>
                    
                    <button 
                        onClick={handleGoBack}
                        className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200"
                    >
                        Go Back
                    </button>
                </div>

                {/* Additional Help */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                        Need help? <Link to="/" className="text-blue-500 hover:text-blue-600 underline">Contact Support</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}
