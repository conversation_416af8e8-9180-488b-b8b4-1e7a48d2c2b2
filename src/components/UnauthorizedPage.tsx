import { Link, useNavigate } from "react-router-dom";

export default function UnauthorizedPage() {
    const navigate = useNavigate();

    const handleGoBack = () => {
        navigate(-1);
    };

    const handleGoHome = () => {
        navigate('/');
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
            <div className="max-w-md w-full text-center">
                {/* 401 Icon */}
                <div className="mb-8">
                    <svg 
                        className="mx-auto h-24 w-24 text-red-500" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                    >
                        <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={1.5} 
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" 
                        />
                    </svg>
                </div>

                {/* Error Message */}
                <h1 className="text-6xl font-bold text-gray-900 mb-4">401</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">Unauthorized Access</h2>
                <p className="text-gray-600 mb-8">
                    You don't have permission to access this resource. Please log in with valid credentials or contact an administrator.
                </p>

                {/* Action Buttons */}
                <div className="space-y-4">
                    <Link 
                        to="/" 
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 inline-block"
                    >
                        Go to Home
                    </Link>
                    
                    <button 
                        onClick={handleGoBack}
                        className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200"
                    >
                        Go Back
                    </button>
                </div>

                {/* Additional Help */}
                <div className="mt-8 pt-8 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                        Need help? <Link to="/" className="text-blue-500 hover:text-blue-600 underline">Contact Support</Link>
                    </p>
                </div>
            </div>
        </div>
    );
}
